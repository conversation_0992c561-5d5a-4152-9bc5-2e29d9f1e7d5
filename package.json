{"name": "gw-api", "version": "2.0.1", "description": "", "author": "NhacVB", "private": true, "license": "Nhacvb-SHDS", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs-modules/mailer": "^1.9.1", "@nestjs/common": "^9.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^9.0.0", "@nestjs/mongoose": "^10.0.10", "@nestjs/platform-express": "^9.0.0", "@nestjs/platform-socket.io": "^9.0.0", "@nestjs/platform-ws": "^9.0.0", "@nestjs/schedule": "^4.0.1", "@nestjs/swagger": "^7.1.15", "@nestjs/typeorm": "^10.0.0", "@nestjs/websockets": "^9.0.0", "axios": "^1.6.2", "axios-retry": "^4.1.0", "bad-words-next": "^2.3.1", "chalk": "^4.1.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "dotenv": "^16.3.1", "express-rate-limit": "^7.2.0", "fastq": "^1.17.1", "flexsearch": "^0.7.43", "ioredis": "^5.4.1", "lodash": "^4.17.21", "lowdb": "1.0.0", "moment": "^2.29.4", "mongoose": "^8.6.3", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.3", "nest-winston": "^1.9.4", "nestjs-ftp": "^0.1.13", "node-cache": "^5.1.2", "promise-retry": "^2.0.1", "reflect-metadata": "^0.1.13", "request-ip": "^3.3.0", "rimraf": "^5.0.0", "rxjs": "^7.2.0", "typeorm": "^0.3.17", "vntk": "^1.4.4", "winston": "^3.11.0", "ws": "^8.13.0", "zeromq": "^6.0.0-beta.16"}, "devDependencies": {"@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/express": "^4.17.13", "@types/jest": "29.2.4", "@types/node": "18.11.18", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "29.3.1", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "29.0.3", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.1.1", "typescript": "^4.7.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "engines": {"node": ">=16"}}